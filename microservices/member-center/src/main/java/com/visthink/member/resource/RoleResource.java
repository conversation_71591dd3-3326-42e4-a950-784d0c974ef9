package com.visthink.member.resource;

import com.visthink.member.service.RoleService;
import com.visthink.member.entity.Role;
import com.visthink.member.dto.RoleCreateRequest;
import com.visthink.member.dto.RoleUpdateRequest;
import com.visthink.member.dto.RolePermissionRequest;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.ApiResponse;
import com.visthink.common.context.TenantContext;

import io.smallrye.mutiny.Uni;
import io.smallrye.common.annotation.Blocking;
import io.quarkus.logging.Log;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import java.util.List;

/**
 * 角色管理REST接口
 * 
 * 提供角色管理的HTTP API接口
 * 包含角色CRUD、权限分配、用户角色管理等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Path("/api/roles")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "角色管理", description = "角色管理相关接口")
public class RoleResource {

    @Inject
    RoleService roleService;

    @Inject
    TenantContext tenantContext;

    // ==================== 基础CRUD操作 ====================

    /**
     * 创建角色
     *
     * 使用@Blocking注解解决响应式上下文中的阻塞操作问题
     * 避免BlockingOperationNotAllowedException异常
     */
    @POST
    @Blocking  // 添加@Blocking注解，允许在响应式上下文中执行阻塞操作
    @Operation(summary = "创建角色", description = "创建新的角色")
    public Uni<ApiResponse<Role>> createRole(@Valid RoleCreateRequest request) {
        Log.infof("创建角色请求: roleCode=%s", request.getRoleCode());

        // 同步获取租户ID，由于添加了@Blocking注解，此操作是安全的
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return roleService.createRole(tenantId, request);
    }

    /**
     * 更新角色
     */
    @PUT
    @Path("/{id}")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    @Operation(summary = "更新角色", description = "更新指定角色的信息")
    public Uni<ApiResponse<Role>> updateRole(
            @Parameter(description = "角色ID") @PathParam("id") Long id,
            @Valid RoleUpdateRequest request) {
        Log.infof("更新角色请求: roleId=%d", id);

        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        request.setId(id);
        return roleService.updateRole(tenantId, id, request);
    }

    /**
     * 删除角色
     */
    @DELETE
    @Path("/{id}")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    @Operation(summary = "删除角色", description = "删除指定的角色")
    public Uni<ApiResponse<Void>> deleteRole(
            @Parameter(description = "角色ID") @PathParam("id") Long id) {
        Log.infof("删除角色请求: roleId=%d", id);

        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return roleService.deleteRole(tenantId, id);
    }

    /**
     * 批量删除角色
     */
    @DELETE
    @Path("/batch")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    @Operation(summary = "批量删除角色", description = "批量删除指定的角色")
    public Uni<ApiResponse<Void>> deleteRoles(List<Long> roleIds) {
        Log.infof("批量删除角色请求: roleIds=%s", roleIds);

        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return roleService.deleteRoles(tenantId, roleIds);
    }

    /**
     * 查询角色详情
     */
    @GET
    @Path("/{id}")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    @Operation(summary = "查询角色详情", description = "根据ID查询角色详细信息")
    public Uni<ApiResponse<Role>> getRoleById(
            @Parameter(description = "角色ID") @PathParam("id") Long id) {
        Log.infof("查询角色详情请求: roleId=%d", id);

        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return roleService.getRoleById(tenantId, id);
    }

    /**
     * 根据编码查询角色
     */
    @GET
    @Path("/code/{roleCode}")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    @Operation(summary = "根据编码查询角色", description = "根据角色编码查询角色信息")
    public Uni<ApiResponse<Role>> getRoleByCode(
            @Parameter(description = "角色编码") @PathParam("roleCode") String roleCode) {
        Log.infof("根据编码查询角色请求: roleCode=%s", roleCode);

        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return roleService.getRoleByCode(tenantId, roleCode);
    }

    // ==================== 查询操作 ====================

    /**
     * 分页查询角色列表
     */
    @GET
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    @Operation(summary = "分页查询角色", description = "分页查询角色列表，支持关键词搜索和条件过滤")
    public Uni<ApiResponse<PageResult<Role>>> getRoleList(
            @Parameter(description = "页码") @QueryParam("page") @DefaultValue("1") int page,
            @Parameter(description = "每页大小") @QueryParam("size") @DefaultValue("10") int size,
            @Parameter(description = "搜索关键词") @QueryParam("keyword") String keyword,
            @Parameter(description = "角色类型") @QueryParam("roleType") Integer roleType,
            @Parameter(description = "状态") @QueryParam("status") Integer status) {

        Log.infof("分页查询角色请求: page=%d, size=%d", page, size);

        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        PageRequest pageRequest = new PageRequest(page, size);
        return roleService.getRoleList(tenantId, pageRequest, keyword, roleType, status);
    }

    /**
     * 查询所有角色
     */
    @GET
    @Path("/all")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    @Operation(summary = "查询所有角色", description = "查询租户下的所有角色")
    public Uni<ApiResponse<List<Role>>> getAllRoles() {
        Log.info("查询所有角色请求");

        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return roleService.getAllRoles(tenantId);
    }

    /**
     * 查询启用的角色
     */
    @GET
    @Path("/enabled")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    @Operation(summary = "查询启用角色", description = "查询租户下所有启用状态的角色")
    public Uni<ApiResponse<List<Role>>> getEnabledRoles() {
        Log.info("查询启用角色请求");

        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return roleService.getEnabledRoles(tenantId);
    }

    /**
     * 查询默认角色
     */
    @GET
    @Path("/default")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    @Operation(summary = "查询默认角色", description = "查询租户下的默认角色")
    public Uni<ApiResponse<List<Role>>> getDefaultRoles() {
        Log.info("查询默认角色请求");

        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return roleService.getDefaultRoles(tenantId);
    }

    /**
     * 根据类型查询角色
     */
    @GET
    @Path("/type/{roleType}")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    @Operation(summary = "根据类型查询角色", description = "根据角色类型查询角色列表")
    public Uni<ApiResponse<List<Role>>> getRolesByType(
            @Parameter(description = "角色类型") @PathParam("roleType") Integer roleType) {
        Log.infof("根据类型查询角色请求: roleType=%d", roleType);

        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return roleService.getRolesByType(tenantId, roleType);
    }

    // ==================== 权限管理 ====================

    /**
     * 分配权限给角色
     */
    @POST
    @Path("/permissions")
    @Operation(summary = "分配权限", description = "分配权限给指定角色")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Void>> assignPermissions(@Valid RolePermissionRequest request) {
        Log.infof("分配权限请求: roleId=%d", request.getRoleId());
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return roleService.assignPermissions(tenantId, request);
    }

    /**
     * 撤销角色权限
     */
    @DELETE
    @Path("/permissions")
    @Operation(summary = "撤销权限", description = "撤销角色的指定权限")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Void>> revokePermissions(@Valid RolePermissionRequest request) {
        Log.infof("撤销权限请求: roleId=%d", request.getRoleId());
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return roleService.revokePermissions(tenantId, request);
    }

    /**
     * 查询角色权限
     */
    @GET
    @Path("/{id}/permissions")
    @Operation(summary = "查询角色权限", description = "查询指定角色的权限列表")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<String>>> getRolePermissions(
            @Parameter(description = "角色ID") @PathParam("id") Long roleId) {
        Log.infof("查询角色权限请求: roleId=%d", roleId);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return roleService.getRolePermissions(tenantId, roleId);
    }

    /**
     * 检查角色权限
     */
    @GET
    @Path("/{id}/permissions/{permissionCode}")
    @Operation(summary = "检查角色权限", description = "检查角色是否拥有指定权限")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Boolean>> hasPermission(
            @Parameter(description = "角色ID") @PathParam("id") Long roleId,
            @Parameter(description = "权限编码") @PathParam("permissionCode") String permissionCode) {
        Log.infof("检查角色权限请求: roleId=%d, permissionCode=%s", roleId, permissionCode);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return roleService.hasPermission(tenantId, roleId, permissionCode);
    }

    // ==================== 状态管理 ====================

    /**
     * 启用角色
     */
    @PUT
    @Path("/{id}/enable")
    @Operation(summary = "启用角色", description = "启用指定的角色")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Void>> enableRole(
            @Parameter(description = "角色ID") @PathParam("id") Long roleId) {
        Log.infof("启用角色请求: roleId=%d", roleId);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return roleService.enableRole(tenantId, roleId);
    }

    /**
     * 禁用角色
     */
    @PUT
    @Path("/{id}/disable")
    @Operation(summary = "禁用角色", description = "禁用指定的角色")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Void>> disableRole(
            @Parameter(description = "角色ID") @PathParam("id") Long roleId) {
        Log.infof("禁用角色请求: roleId=%d", roleId);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return roleService.disableRole(tenantId, roleId);
    }

    // ==================== 验证接口 ====================

    /**
     * 检查角色编码是否存在
     */
    @GET
    @Path("/check-code")
    @Operation(summary = "检查角色编码", description = "检查角色编码是否已存在")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Boolean>> checkRoleCodeExists(
            @Parameter(description = "角色编码") @QueryParam("roleCode") String roleCode,
            @Parameter(description = "排除的角色ID") @QueryParam("excludeId") Long excludeId) {
        Log.infof("检查角色编码请求: roleCode=%s", roleCode);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return roleService.checkRoleCodeExists(tenantId, roleCode, excludeId);
    }

    // ==================== 统计接口 ====================

    /**
     * 统计角色数量
     */
    @GET
    @Path("/count")
    @Operation(summary = "统计角色数量", description = "统计租户下的角色总数")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Long>> countRoles() {
        Log.info("统计角色数量请求");
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return roleService.countRoles(tenantId);
    }
}
