package com.visthink.member.resource;

import com.visthink.member.service.PermissionService;
import com.visthink.member.entity.Permission;
import com.visthink.member.dto.PermissionCreateRequest;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.ApiResponse;
import com.visthink.common.context.TenantContext;

import io.smallrye.mutiny.Uni;
import io.smallrye.common.annotation.Blocking;
import io.quarkus.logging.Log;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import java.util.List;

/**
 * 权限管理REST接口
 * 
 * 提供权限管理的HTTP API接口
 * 包含权限CRUD、权限树查询、用户权限验证等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Path("/api/permissions")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "权限管理", description = "权限管理相关接口")
public class PermissionResource {

    @Inject
    PermissionService permissionService;

    @Inject
    TenantContext tenantContext;

    // ==================== 基础CRUD操作 ====================

    /**
     * 创建权限
     */
    @POST
    @Operation(summary = "创建权限", description = "创建新的权限")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Permission>> createPermission(@Valid PermissionCreateRequest request) {
        Log.infof("创建权限请求: permissionCode=%s", request.getPermissionCode());
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        // 转换DTO为实体
        Permission permission = convertToEntity(request);
        return permissionService.createPermission(tenantId, permission);
    }

    /**
     * 更新权限
     */
    @PUT
    @Path("/{id}")
    @Operation(summary = "更新权限", description = "更新指定权限的信息")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Permission>> updatePermission(
            @Parameter(description = "权限ID") @PathParam("id") Long id,
            @Valid PermissionCreateRequest request) {
        Log.infof("更新权限请求: permissionId=%d", id);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        // 转换DTO为实体
        Permission permission = convertToEntity(request);
        permission.id = id;
        return permissionService.updatePermission(tenantId, id, permission);
    }

    /**
     * 删除权限
     */
    @DELETE
    @Path("/{id}")
    @Operation(summary = "删除权限", description = "删除指定的权限")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Void>> deletePermission(
            @Parameter(description = "权限ID") @PathParam("id") Long id) {
        Log.infof("删除权限请求: permissionId=%d", id);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return permissionService.deletePermission(tenantId, id);
    }

    /**
     * 批量删除权限
     */
    @DELETE
    @Path("/batch")
    @Operation(summary = "批量删除权限", description = "批量删除指定的权限")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Void>> deletePermissions(List<Long> permissionIds) {
        Log.infof("批量删除权限请求: permissionIds=%s", permissionIds);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return permissionService.deletePermissions(tenantId, permissionIds);
    }

    /**
     * 查询权限详情
     */
    @GET
    @Path("/{id}")
    @Operation(summary = "查询权限详情", description = "根据ID查询权限详细信息")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Permission>> getPermissionById(
            @Parameter(description = "权限ID") @PathParam("id") Long id) {
        Log.infof("查询权限详情请求: permissionId=%d", id);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return permissionService.getPermissionById(tenantId, id);
    }

    /**
     * 根据编码查询权限
     */
    @GET
    @Path("/code/{permissionCode}")
    @Operation(summary = "根据编码查询权限", description = "根据权限编码查询权限信息")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Permission>> getPermissionByCode(
            @Parameter(description = "权限编码") @PathParam("permissionCode") String permissionCode) {
        Log.infof("根据编码查询权限请求: permissionCode=%s", permissionCode);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return permissionService.getPermissionByCode(tenantId, permissionCode);
    }

    // ==================== 查询操作 ====================

    /**
     * 分页查询权限列表
     */
    @GET
    @Operation(summary = "分页查询权限", description = "分页查询权限列表，支持关键词搜索和条件过滤")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<PageResult<Permission>>> getPermissionList(
            @Parameter(description = "页码") @QueryParam("page") @DefaultValue("1") int page,
            @Parameter(description = "每页大小") @QueryParam("size") @DefaultValue("10") int size,
            @Parameter(description = "搜索关键词") @QueryParam("keyword") String keyword,
            @Parameter(description = "权限类型") @QueryParam("permissionType") Integer permissionType,
            @Parameter(description = "状态") @QueryParam("status") Integer status) {
        
        Log.infof("分页查询权限请求: page=%d, size=%d", page, size);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        PageRequest pageRequest = new PageRequest(page, size);
        return permissionService.getPermissionList(tenantId, pageRequest, keyword, permissionType, status);
    }

    /**
     * 查询所有权限
     */
    @GET
    @Path("/all")
    @Operation(summary = "查询所有权限", description = "查询租户下的所有权限")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<Permission>>> getAllPermissions() {
        Log.info("查询所有权限请求");
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return permissionService.getAllPermissions(tenantId);
    }

    /**
     * 查询权限树
     */
    @GET
    @Path("/tree")
    @Operation(summary = "查询权限树", description = "查询租户下的权限树结构")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<Permission>>> getPermissionTree() {
        Log.info("查询权限树请求");
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return permissionService.getPermissionTree(tenantId);
    }

    /**
     * 查询根权限
     */
    @GET
    @Path("/root")
    @Operation(summary = "查询根权限", description = "查询租户下的根权限")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<Permission>>> getRootPermissions() {
        Log.info("查询根权限请求");
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return permissionService.getRootPermissions(tenantId);
    }

    /**
     * 查询子权限
     */
    @GET
    @Path("/{parentId}/children")
    @Operation(summary = "查询子权限", description = "查询指定权限的子权限")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<Permission>>> getChildPermissions(
            @Parameter(description = "父权限ID") @PathParam("parentId") Long parentId) {
        Log.infof("查询子权限请求: parentId=%d", parentId);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return permissionService.getChildPermissions(tenantId, parentId);
    }

    /**
     * 根据类型查询权限
     */
    @GET
    @Path("/type/{permissionType}")
    @Operation(summary = "根据类型查询权限", description = "根据权限类型查询权限列表")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<Permission>>> getPermissionsByType(
            @Parameter(description = "权限类型") @PathParam("permissionType") Integer permissionType) {
        Log.infof("根据类型查询权限请求: permissionType=%d", permissionType);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return permissionService.getPermissionsByType(tenantId, permissionType);
    }

    /**
     * 查询系统权限
     */
    @GET
    @Path("/system")
    @Operation(summary = "查询系统权限", description = "查询系统内置权限")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<Permission>>> getSystemPermissions() {
        Log.info("查询系统权限请求");
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return permissionService.getSystemPermissions(tenantId);
    }

    // ==================== 用户权限查询 ====================

    /**
     * 查询用户权限
     */
    @GET
    @Path("/user/{userId}")
    @Operation(summary = "查询用户权限", description = "查询指定用户的权限列表")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<Permission>>> getUserPermissions(
            @Parameter(description = "用户ID") @PathParam("userId") Long userId) {
        Log.infof("查询用户权限请求: userId=%d", userId);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return permissionService.getUserPermissions(tenantId, userId);
    }

    /**
     * 查询用户权限编码
     */
    @GET
    @Path("/user/{userId}/codes")
    @Operation(summary = "查询用户权限编码", description = "查询指定用户的权限编码列表")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<String>>> getUserPermissionCodes(
            @Parameter(description = "用户ID") @PathParam("userId") Long userId) {
        Log.infof("查询用户权限编码请求: userId=%d", userId);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return permissionService.getUserPermissionCodes(tenantId, userId);
    }

    /**
     * 检查用户权限
     */
    @GET
    @Path("/user/{userId}/check/{permissionCode}")
    @Operation(summary = "检查用户权限", description = "检查用户是否拥有指定权限")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Boolean>> hasUserPermission(
            @Parameter(description = "用户ID") @PathParam("userId") Long userId,
            @Parameter(description = "权限编码") @PathParam("permissionCode") String permissionCode) {
        Log.infof("检查用户权限请求: userId=%d, permissionCode=%s", userId, permissionCode);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return permissionService.hasUserPermission(tenantId, userId, permissionCode);
    }

    // ==================== 验证接口 ====================

    /**
     * 验证权限编码格式
     */
    @GET
    @Path("/validate-code")
    @Operation(summary = "验证权限编码", description = "验证权限编码格式是否正确")
    public ApiResponse<Boolean> validatePermissionCode(
            @Parameter(description = "权限编码") @QueryParam("permissionCode") String permissionCode) {
        Log.infof("验证权限编码请求: permissionCode=%s", permissionCode);
        
        return permissionService.validatePermissionCode(permissionCode);
    }

    /**
     * 检查权限编码是否存在
     */
    @GET
    @Path("/check-code")
    @Operation(summary = "检查权限编码", description = "检查权限编码是否已存在")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Boolean>> checkPermissionCodeExists(
            @Parameter(description = "权限编码") @QueryParam("permissionCode") String permissionCode,
            @Parameter(description = "排除的权限ID") @QueryParam("excludeId") Long excludeId) {
        Log.infof("检查权限编码请求: permissionCode=%s", permissionCode);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return permissionService.checkPermissionCodeExists(tenantId, permissionCode, excludeId);
    }

    // ==================== 统计接口 ====================

    /**
     * 统计权限数量
     */
    @GET
    @Path("/count")
    @Operation(summary = "统计权限数量", description = "统计租户下的权限总数")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Long>> countPermissions() {
        Log.info("统计权限数量请求");
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return permissionService.countPermissions(tenantId);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 转换DTO为实体
     */
    private Permission convertToEntity(PermissionCreateRequest request) {
        Permission permission = new Permission();
        permission.setPermissionCode(request.getPermissionCode());
        permission.setPermissionName(request.getPermissionName());
        permission.setPermissionType(request.getPermissionType());
        permission.setParentId(request.getParentId());
        permission.setResourceType(request.getResourceType());
        permission.setResourcePath(request.getResourcePath());
        permission.setAction(request.getAction());
        permission.setDescription(request.getDescription());
        permission.setIsSystem(request.getIsSystem());
        permission.setSortOrder(request.getSortOrder());
        permission.setRemark(request.getRemark());
        
        // 自动设置资源类型和操作
        request.autoSetResourceAndAction();
        if (permission.getResourceType() == null) {
            permission.setResourceType(request.getResourceType());
        }
        if (permission.getAction() == null) {
            permission.setAction(request.getAction());
        }
        
        return permission;
    }
}
