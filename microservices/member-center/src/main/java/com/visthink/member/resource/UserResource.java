package com.visthink.member.resource;

import com.visthink.member.entity.UserAccount;
import com.visthink.member.service.UserService;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.ApiResponse;
import com.visthink.common.context.TenantContext;
import io.smallrye.mutiny.Uni;
import io.smallrye.common.annotation.Blocking;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

/**
 * 用户管理 REST API
 * 提供用户的增删改查功能，支持多租户数据隔离
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Path("/api/users")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "用户管理", description = "用户管理相关接口")
public class UserResource {

    @Inject
    UserService userService;

    @Inject
    TenantContext tenantContext;

    /**
     * 分页查询用户列表
     * 支持按关键词搜索和状态过滤
     *
     * @param page 页码，从1开始
     * @param size 每页大小
     * @param keyword 搜索关键词（可选）
     * @param status 用户状态（可选）
     * @return 分页用户列表
     */
    @GET
    @Operation(summary = "分页查询用户列表", description = "支持按关键词搜索和状态过滤的分页查询")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<PageResult<UserAccount>>> getUsers(
            @QueryParam("page") @DefaultValue("1") int page,
            @QueryParam("size") @DefaultValue("10") int size,
            @QueryParam("keyword") String keyword,
            @QueryParam("status") Integer status) {
        
        // 获取当前租户ID
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        // 构建分页请求
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(page);
        pageRequest.setSize(size);

        // 调用服务层查询
        return userService.findPageByConditions(tenantId, keyword, status, pageRequest)
            .onItem().transform(result -> ApiResponse.success(result))
            .onFailure().recoverWithItem(throwable -> {
                throwable.printStackTrace();
                return ApiResponse.error("查询用户列表失败: " + throwable.getMessage());
            });
    }

    /**
     * 根据ID查询用户详情
     *
     * @param id 用户ID
     * @return 用户详情
     */
    @GET
    @Path("/{id}")
    @Operation(summary = "查询用户详情", description = "根据用户ID查询用户详细信息")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<UserAccount>> getUserById(@PathParam("id") Long id) {
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return userService.findByIdAndTenantId(id, tenantId)
            .onItem().transform(user -> {
                if (user != null) {
                    return ApiResponse.success(user);
                } else {
                    return ApiResponse.<UserAccount>error("用户不存在");
                }
            })
            .onFailure().recoverWithItem(throwable -> {
                throwable.printStackTrace();
                return ApiResponse.<UserAccount>error("查询用户详情失败: " + throwable.getMessage());
            });
    }

    /**
     * 创建新用户
     *
     * @param userAccount 用户信息
     * @return 创建结果
     */
    @POST
    @Operation(summary = "创建用户", description = "创建新的用户账户")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<UserAccount>> createUser(UserAccount userAccount) {
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        // 设置租户ID
        userAccount.tenantId = tenantId;

        return userService.createUserInternal(userAccount)
            .onItem().transform(user -> ApiResponse.success(user))
            .onFailure().recoverWithItem(throwable -> {
                throwable.printStackTrace();
                return ApiResponse.error("创建用户失败: " + throwable.getMessage());
            });
    }

    /**
     * 更新用户信息
     *
     * @param id 用户ID
     * @param userAccount 用户信息
     * @return 更新结果
     */
    @PUT
    @Path("/{id}")
    @Operation(summary = "更新用户", description = "更新用户信息")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<UserAccount>> updateUser(@PathParam("id") Long id, UserAccount userAccount) {
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        // 设置ID和租户ID
        userAccount.id = id;
        userAccount.tenantId = tenantId;

        return userService.updateUser(userAccount)
            .onItem().transform(user -> ApiResponse.success(user))
            .onFailure().recoverWithItem(throwable -> {
                throwable.printStackTrace();
                return ApiResponse.error("更新用户失败: " + throwable.getMessage());
            });
    }

    /**
     * 删除用户（软删除）
     *
     * @param id 用户ID
     * @return 删除结果
     */
    @DELETE
    @Path("/{id}")
    @Operation(summary = "删除用户", description = "软删除用户（标记为已删除）")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Boolean>> deleteUser(@PathParam("id") Long id) {
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return userService.deleteUser(id, tenantId)
            .onItem().transform(result -> ApiResponse.success(result))
            .onFailure().recoverWithItem(throwable -> {
                throwable.printStackTrace();
                return ApiResponse.error("删除用户失败: " + throwable.getMessage());
            });
    }

    /**
     * 检查用户名是否可用
     *
     * @param username 用户名
     * @return 是否可用
     */
    @GET
    @Path("/check-username")
    @Operation(summary = "检查用户名", description = "检查用户名是否已被使用")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Boolean>> checkUsername(@QueryParam("username") String username) {
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        if (username == null || username.trim().isEmpty()) {
            return Uni.createFrom().item(ApiResponse.error("用户名不能为空"));
        }

        return userService.isUsernameAvailable(tenantId, username, null)
            .onItem().transform(available -> ApiResponse.success(available))
            .onFailure().recoverWithItem(throwable -> {
                throwable.printStackTrace();
                return ApiResponse.error("检查用户名失败: " + throwable.getMessage());
            });
    }

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    @GET
    @Path("/by-username/{username}")
    @Operation(summary = "根据用户名查询", description = "根据用户名查询用户信息")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<UserAccount>> getUserByUsername(@PathParam("username") String username) {
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return userService.findByUsername(tenantId, username)
            .onItem().transform(user -> {
                if (user != null) {
                    return ApiResponse.success(user);
                } else {
                    return ApiResponse.<UserAccount>error("用户不存在");
                }
            })
            .onFailure().recoverWithItem(throwable -> {
                throwable.printStackTrace();
                return ApiResponse.<UserAccount>error("查询用户失败: " + throwable.getMessage());
            });
    }
}
