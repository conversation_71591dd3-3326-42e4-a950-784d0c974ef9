package com.visthink.member.resource;

import com.visthink.member.service.MenuService;
import com.visthink.member.entity.Menu;
import com.visthink.member.dto.MenuCreateRequest;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.ApiResponse;
import com.visthink.common.context.TenantContext;

import io.smallrye.mutiny.Uni;
import io.smallrye.common.annotation.Blocking;
import io.quarkus.logging.Log;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import java.util.List;
import java.util.Map;

/**
 * 菜单管理REST接口
 * 
 * 提供菜单管理的HTTP API接口
 * 包含菜单CRUD、菜单树查询、用户菜单查询等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Path("/api/menus")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "菜单管理", description = "菜单管理相关接口")
public class MenuResource {

    @Inject
    MenuService menuService;

    @Inject
    TenantContext tenantContext;

    // ==================== 基础CRUD操作 ====================

    /**
     * 创建菜单
     */
    @POST
    @Operation(summary = "创建菜单", description = "创建新的菜单")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Menu>> createMenu(@Valid MenuCreateRequest request) {
        Log.infof("创建菜单请求: menuCode=%s", request.getMenuCode());
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        // 转换DTO为实体
        Menu menu = convertToEntity(request);
        return menuService.createMenu(tenantId, menu);
    }

    /**
     * 更新菜单
     */
    @PUT
    @Path("/{id}")
    @Operation(summary = "更新菜单", description = "更新指定菜单的信息")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Menu>> updateMenu(
            @Parameter(description = "菜单ID") @PathParam("id") Long id,
            @Valid MenuCreateRequest request) {
        Log.infof("更新菜单请求: menuId=%d", id);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        // 转换DTO为实体
        Menu menu = convertToEntity(request);
        menu.id = id;
        return menuService.updateMenu(tenantId, id, menu);
    }

    /**
     * 删除菜单
     */
    @DELETE
    @Path("/{id}")
    @Operation(summary = "删除菜单", description = "删除指定的菜单")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Void>> deleteMenu(
            @Parameter(description = "菜单ID") @PathParam("id") Long id) {
        Log.infof("删除菜单请求: menuId=%d", id);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return menuService.deleteMenu(tenantId, id);
    }

    /**
     * 批量删除菜单
     */
    @DELETE
    @Path("/batch")
    @Operation(summary = "批量删除菜单", description = "批量删除指定的菜单")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Void>> deleteMenus(List<Long> menuIds) {
        Log.infof("批量删除菜单请求: menuIds=%s", menuIds);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return menuService.deleteMenus(tenantId, menuIds);
    }

    /**
     * 查询菜单详情
     */
    @GET
    @Path("/{id}")
    @Operation(summary = "查询菜单详情", description = "根据ID查询菜单详细信息")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Menu>> getMenuById(
            @Parameter(description = "菜单ID") @PathParam("id") Long id) {
        Log.infof("查询菜单详情请求: menuId=%d", id);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return menuService.getMenuById(tenantId, id);
    }

    /**
     * 根据编码查询菜单
     */
    @GET
    @Path("/code/{menuCode}")
    @Operation(summary = "根据编码查询菜单", description = "根据菜单编码查询菜单信息")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Menu>> getMenuByCode(
            @Parameter(description = "菜单编码") @PathParam("menuCode") String menuCode) {
        Log.infof("根据编码查询菜单请求: menuCode=%s", menuCode);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return menuService.getMenuByCode(tenantId, menuCode);
    }

    // ==================== 查询操作 ====================

    /**
     * 分页查询菜单列表
     */
    @GET
    @Operation(summary = "分页查询菜单", description = "分页查询菜单列表，支持关键词搜索和条件过滤")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<PageResult<Menu>>> getMenuList(
            @Parameter(description = "页码") @QueryParam("page") @DefaultValue("1") int page,
            @Parameter(description = "每页大小") @QueryParam("size") @DefaultValue("10") int size,
            @Parameter(description = "搜索关键词") @QueryParam("keyword") String keyword,
            @Parameter(description = "菜单类型") @QueryParam("menuType") Integer menuType,
            @Parameter(description = "状态") @QueryParam("status") Integer status) {
        
        Log.infof("分页查询菜单请求: page=%d, size=%d", page, size);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        PageRequest pageRequest = new PageRequest(page, size);
        return menuService.getMenuList(tenantId, pageRequest, keyword, menuType, status);
    }

    /**
     * 查询所有菜单
     */
    @GET
    @Path("/all")
    @Operation(summary = "查询所有菜单", description = "查询租户下的所有菜单")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<Menu>>> getAllMenus() {
        Log.info("查询所有菜单请求");
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return menuService.getAllMenus(tenantId);
    }

    /**
     * 查询菜单树
     */
    @GET
    @Path("/tree")
    @Operation(summary = "查询菜单树", description = "查询租户下的菜单树结构")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<Menu>>> getMenuTree() {
        Log.info("查询菜单树请求");
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return menuService.getMenuTree(tenantId);
    }

    /**
     * 查询可见菜单树
     */
    @GET
    @Path("/tree/visible")
    @Operation(summary = "查询可见菜单树", description = "查询租户下的可见菜单树结构")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<Menu>>> getVisibleMenuTree() {
        Log.info("查询可见菜单树请求");
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return menuService.getVisibleMenuTree(tenantId);
    }

    /**
     * 查询根菜单
     */
    @GET
    @Path("/root")
    @Operation(summary = "查询根菜单", description = "查询租户下的根菜单")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<Menu>>> getRootMenus() {
        Log.info("查询根菜单请求");
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return menuService.getRootMenus(tenantId);
    }

    /**
     * 查询子菜单
     */
    @GET
    @Path("/{parentId}/children")
    @Operation(summary = "查询子菜单", description = "查询指定菜单的子菜单")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<Menu>>> getChildMenus(
            @Parameter(description = "父菜单ID") @PathParam("parentId") Long parentId) {
        Log.infof("查询子菜单请求: parentId=%d", parentId);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return menuService.getChildMenus(tenantId, parentId);
    }

    /**
     * 根据类型查询菜单
     */
    @GET
    @Path("/type/{menuType}")
    @Operation(summary = "根据类型查询菜单", description = "根据菜单类型查询菜单列表")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<Menu>>> getMenusByType(
            @Parameter(description = "菜单类型") @PathParam("menuType") Integer menuType) {
        Log.infof("根据类型查询菜单请求: menuType=%d", menuType);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return menuService.getMenusByType(tenantId, menuType);
    }

    // ==================== 用户菜单查询 ====================

    /**
     * 查询用户菜单
     */
    @GET
    @Path("/user/{userId}")
    @Operation(summary = "查询用户菜单", description = "查询指定用户的菜单列表")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<Menu>>> getUserMenus(
            @Parameter(description = "用户ID") @PathParam("userId") Long userId) {
        Log.infof("查询用户菜单请求: userId=%d", userId);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return menuService.getUserMenus(tenantId, userId);
    }

    /**
     * 查询用户菜单树
     */
    @GET
    @Path("/user/{userId}/tree")
    @Operation(summary = "查询用户菜单树", description = "查询指定用户的菜单树结构")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<Menu>>> getUserMenuTree(
            @Parameter(description = "用户ID") @PathParam("userId") Long userId) {
        Log.infof("查询用户菜单树请求: userId=%d", userId);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return menuService.getUserMenuTree(tenantId, userId);
    }

    /**
     * 查询用户菜单编码
     */
    @GET
    @Path("/user/{userId}/codes")
    @Operation(summary = "查询用户菜单编码", description = "查询指定用户的菜单编码列表")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<String>>> getUserMenuCodes(
            @Parameter(description = "用户ID") @PathParam("userId") Long userId) {
        Log.infof("查询用户菜单编码请求: userId=%d", userId);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return menuService.getUserMenuCodes(tenantId, userId);
    }

    /**
     * 检查用户菜单访问权限
     */
    @GET
    @Path("/user/{userId}/check/{menuCode}")
    @Operation(summary = "检查用户菜单权限", description = "检查用户是否可访问指定菜单")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<Boolean>> hasUserMenuAccess(
            @Parameter(description = "用户ID") @PathParam("userId") Long userId,
            @Parameter(description = "菜单编码") @PathParam("menuCode") String menuCode) {
        Log.infof("检查用户菜单权限请求: userId=%d, menuCode=%s", userId, menuCode);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return menuService.hasUserMenuAccess(tenantId, userId, menuCode);
    }

    // ==================== 菜单权限管理 ====================

    /**
     * 根据权限查询菜单
     */
    @GET
    @Path("/permission/{permissionCode}")
    @Operation(summary = "根据权限查询菜单", description = "根据权限编码查询菜单列表")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<Menu>>> getMenusByPermission(
            @Parameter(description = "权限编码") @PathParam("permissionCode") String permissionCode) {
        Log.infof("根据权限查询菜单请求: permissionCode=%s", permissionCode);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return menuService.getMenusByPermission(tenantId, permissionCode);
    }

    /**
     * 查询菜单按钮
     */
    @GET
    @Path("/{menuId}/buttons")
    @Operation(summary = "查询菜单按钮", description = "查询指定菜单的按钮权限")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<Menu>>> getMenuButtons(
            @Parameter(description = "菜单ID") @PathParam("menuId") Long menuId) {
        Log.infof("查询菜单按钮请求: menuId=%d", menuId);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return menuService.getMenuButtons(tenantId, menuId);
    }

    /**
     * 查询用户菜单按钮
     */
    @GET
    @Path("/{menuId}/user/{userId}/buttons")
    @Operation(summary = "查询用户菜单按钮", description = "查询用户在指定菜单下的按钮权限")
    @Blocking  // 添加@Blocking注解，解决阻塞操作问题
    public Uni<ApiResponse<List<Menu>>> getUserMenuButtons(
            @Parameter(description = "菜单ID") @PathParam("menuId") Long menuId,
            @Parameter(description = "用户ID") @PathParam("userId") Long userId) {
        Log.infof("查询用户菜单按钮请求: menuId=%d, userId=%d", menuId, userId);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return menuService.getUserMenuButtons(tenantId, userId, menuId);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 转换DTO为实体
     */
    private Menu convertToEntity(MenuCreateRequest request) {
        Menu menu = new Menu();
        menu.setMenuCode(request.getMenuCode());
        menu.setMenuName(request.getMenuName());
        menu.setMenuType(request.getMenuType());
        menu.setParentId(request.getParentId());
        menu.setMenuPath(request.getMenuPath());
        menu.setComponentPath(request.getComponentPath());
        menu.setMenuIcon(request.getMenuIcon());
        menu.setPermissionCode(request.getPermissionCode());
        menu.setIsExternal(request.getIsExternal());
        menu.setIsCached(request.getIsCached());
        menu.setIsVisible(request.getIsVisible());
        menu.setDescription(request.getDescription());
        menu.setMetaData(request.getMetaData());
        menu.setSortOrder(request.getSortOrder());
        menu.setRemark(request.getRemark());
        
        // 设置按钮权限（如果有）
        if (request.getButtonPermissions() != null && !request.getButtonPermissions().isEmpty()) {
            // 将按钮权限转换为JSON字符串存储
            // 这里简化处理，实际应该使用JSON序列化
            String buttonPermissionsJson = request.getButtonPermissions().toString();
            menu.setButtonPermissions(buttonPermissionsJson);
        }
        
        // 设置默认值
        request.setDefaults();
        
        return menu;
    }
}
