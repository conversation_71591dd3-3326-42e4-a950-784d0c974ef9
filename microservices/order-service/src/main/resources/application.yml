# 订单服务配置文件
# 服务名称和端口配置
quarkus:
  application:
    name: order-service
    version: 1.0.0

  # HTTP服务配置
  http:
    port: 8084
    host: 0.0.0.0
    cors:
      ~: true
      origins: "*"
      methods: "GET,PUT,POST,DELETE,OPTIONS"
      headers: "accept, authorization, content-type, x-requested-with, x-tenant-id"
      exposed-headers: "Content-Disposition,x-total-count"

  # 数据源配置 - PostgreSQL响应式
  datasource:
    reactive:
      url: ${DB_URL:vertx-reactive:postgresql://*************:35432/visthink_order}
      max-size: 20
      username: postgres
      password: ${DB_PASSWORD:zylp}
      idle-timeout: PT10M
      max-lifetime: PT30M

  # Hibernate配置
  hibernate-orm:
    database:
      generation: update
    log:
      sql: false
      format-sql: true
      bind-parameters: true



  # Redis配置
  redis:
    hosts: ${REDIS_URL:redis://127.0.0.1:6379/0}
    password: ${REDIS_PASSWORD:zylp}
    timeout: 10s
    max-pool-size: 20
    max-pool-waiting: 24
    devservices:
      enabled: false

  # OpenAPI文档配置
  smallrye-openapi:
    info-title: 订单管理服务 API
    info-version: 1.0.0
    info-description: Visthink ERP 订单管理服务，提供订单生命周期管理、支付处理、库存集成等功能
    info-contact-name: Visthink开发团队
    info-contact-email: <EMAIL>
    servers:
      - url: http://localhost:8084
        description: 开发环境
      - url: http://order-service:8084
        description: 容器环境

  # 健康检查配置
  smallrye-health:
    root-path: /health

  # 监控配置
  micrometer:
    binder:
      http-server:
        enabled: true
      jvm:
        enabled: true
      system:
        enabled: true
    export:
      prometheus:
        enabled: true
        path: /metrics

  # REST客户端配置
  rest-client:
    # 商品服务客户端
    product-service:
      url: ${PRODUCT_SERVICE_URL:http://localhost:8082}
      scope: jakarta.inject.Singleton
      connect-timeout: 5000
      read-timeout: 30000

    # 库存服务客户端
    inventory-service:
      url: ${INVENTORY_SERVICE_URL:http://localhost:8083}
      scope: jakarta.inject.Singleton
      connect-timeout: 5000
      read-timeout: 30000

    # 用户服务客户端
    member-service:
      url: ${MEMBER_CENTER_URL:http://localhost:8081}
      scope: jakarta.inject.Singleton
      connect-timeout: 5000
      read-timeout: 30000

  # 服务发现配置
  consul-config:
    enabled: ${CONSUL_ENABLED:false}
    agent:
      host-port: ${CONSUL_URL:localhost:8500}
    config-properties:
      prefix: config/order-service

  # 链路追踪配置
  opentelemetry:
    enabled: ${TRACING_ENABLED:false}
    tracer:
      exporter:
        jaeger:
          endpoint: ${JAEGER_ENDPOINT:http://localhost:14268/api/traces}
    service-name: order-service

  # 日志配置
  log:
    level: INFO
    category:
      com.visthink: DEBUG
      io.quarkus.hibernate: INFO
      org.hibernate: WARN
    console:
      enable: true
      format: "%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{3.}] (%t) %s%e%n"

  # 本地开发配置
  dev:
    ui:
      always-include: true

  # 测试配置
  test:
    hang-detection-timeout: 60s

# 业务配置
app:
  # 订单配置
  order:
    # 订单号生成配置
    order-number:
      prefix: "ORD"
      date-format: "yyyyMMdd"
      sequence-length: 6

    # 订单超时配置（分钟）
    timeout:
      unpaid: 30        # 未支付订单超时时间
      unshipped: 2880   # 未发货订单超时时间（48小时）
      unreceived: 10080 # 未收货订单超时时间（7天）

    # 订单状态配置
    status:
      auto-confirm: true    # 是否自动确认订单
      auto-complete: false  # 是否自动完成订单

  # 支付配置
  payment:
    timeout: 30  # 支付超时时间（分钟）
    retry-times: 3  # 支付重试次数

  # 库存配置
  inventory:
    reserve-timeout: 30  # 库存预占超时时间（分钟）
    auto-release: true   # 是否自动释放超时预占库存

# 外部服务配置
external:
  # 商品服务
  product-service:
    url: ${PRODUCT_SERVICE_URL:http://localhost:8082}
    timeout: 10s
    retry-times: 3

  # 库存服务
  inventory-service:
    url: ${INVENTORY_SERVICE_URL:http://localhost:8083}
    timeout: 10s
    retry-times: 3

  # 用户服务
  member-center:
    url: ${MEMBER_CENTER_URL:http://localhost:8081}
    timeout: 10s
    retry-times: 3



# 环境特定配置
"%dev":
  quarkus:
    log:
      category:
        com.visthink: DEBUG
        io.quarkus.hibernate: DEBUG
    hibernate-orm:
      log:
        sql: true

"%test":
  quarkus:
    datasource:
      reactive:
        url: vertx-reactive:h2:mem:test;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
        username: sa
        password: ""


"%prod":
  quarkus:
    log:
      level: INFO
      category:
        com.visthink: INFO
    consul-config:
      enabled: true
    opentelemetry:
      enabled: true
