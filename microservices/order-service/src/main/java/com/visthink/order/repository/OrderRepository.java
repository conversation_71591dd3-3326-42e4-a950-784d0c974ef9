package com.visthink.order.repository;

import com.visthink.order.entity.Order;
import com.visthink.common.repository.ReactiveBaseRepository;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单数据访问接口
 *
 * 提供订单相关的数据库操作，包括基础CRUD、状态查询、统计分析等功能
 * 继承ReactiveBaseRepository获得通用的数据访问能力，支持纯Hibernate Reactive操作
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class OrderRepository extends ReactiveBaseRepository {

    /**
     * 根据ID查找订单
     *
     * @param id 订单ID
     * @return 订单对象
     */
    public Uni<Order> findById(Long id) {
        return super.findById(id, Order.class);
    }

    /**
     * 根据条件查询订单列表
     *
     * @param field 字段名
     * @param value 字段值
     * @return 订单列表
     */
    public Uni<List<Order>> list(String field, Object value) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Order WHERE " + field + " = ?1 AND deleted = 0",
                Order.class
            );
            query.setParameter(1, value);
            return query.getResultList();
        });
    }

    /**
     * 删除订单
     *
     * @param order 订单对象
     * @return 删除结果
     */
    public Uni<Void> delete(Order order) {
        return executeTransaction(session -> {
            return session.remove(order);
        });
    }

    /**
     * 根据ID删除订单
     *
     * @param id 订单ID
     * @return 删除结果
     */
    public Uni<Boolean> deleteById(Long id) {
        return update("UPDATE Order SET deleted = 1 WHERE id = ?1", id)
                .map(count -> count > 0);
    }

    /**
     * 根据订单号查询订单
     *
     * @param tenantId 租户ID
     * @param orderNumber 订单号
     * @return 订单对象
     */
    public Uni<Order> findByOrderNumber(Long tenantId, String orderNumber) {
        return findSingle("FROM Order WHERE tenantId = ?1 AND orderNumber = ?2 AND deleted = 0",
                         Order.class, tenantId, orderNumber);
    }

    /**
     * 根据用户ID查询订单列表
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 订单列表
     */
    public Uni<List<Order>> findByUserId(Long tenantId, Long userId) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Order WHERE tenantId = ?1 AND userId = ?2 AND deleted = 0 ORDER BY createTime DESC",
                Order.class
            );
            query.setParameter(1, tenantId);
            query.setParameter(2, userId);
            return query.getResultList();
        });
    }

    /**
     * 根据用户ID分页查询订单
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param page 页码（从0开始）
     * @param size 页大小
     * @return 订单列表
     */
    public Uni<List<Order>> findByUserIdWithPage(Long tenantId, Long userId, int page, int size) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Order WHERE tenantId = ?1 AND userId = ?2 AND deleted = 0 ORDER BY createTime DESC",
                Order.class
            );
            query.setParameter(1, tenantId);
            query.setParameter(2, userId);
            query.setFirstResult(page * size);
            query.setMaxResults(size);
            return query.getResultList();
        });
    }

    /**
     * 根据订单状态查询订单列表
     *
     * @param tenantId 租户ID
     * @param orderStatus 订单状态
     * @return 订单列表
     */
    public Uni<List<Order>> findByOrderStatus(Long tenantId, Integer orderStatus) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Order WHERE tenantId = ?1 AND orderStatus = ?2 AND deleted = 0 ORDER BY createTime DESC",
                Order.class
            );
            query.setParameter(1, tenantId);
            query.setParameter(2, orderStatus);
            return query.getResultList();
        });
    }

    /**
     * 根据支付状态查询订单列表
     *
     * @param tenantId 租户ID
     * @param paymentStatus 支付状态
     * @return 订单列表
     */
    public Uni<List<Order>> findByPaymentStatus(Long tenantId, Integer paymentStatus) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Order WHERE tenantId = ?1 AND paymentStatus = ?2 AND deleted = 0 ORDER BY createTime DESC",
                Order.class
            );
            query.setParameter(1, tenantId);
            query.setParameter(2, paymentStatus);
            return query.getResultList();
        });
    }

    /**
     * 根据发货状态查询订单列表
     *
     * @param tenantId 租户ID
     * @param shippingStatus 发货状态
     * @return 订单列表
     */
    public Uni<List<Order>> findByShippingStatus(Long tenantId, Integer shippingStatus) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Order WHERE tenantId = ?1 AND shippingStatus = ?2 AND deleted = 0 ORDER BY createTime DESC",
                Order.class
            );
            query.setParameter(1, tenantId);
            query.setParameter(2, shippingStatus);
            return query.getResultList();
        });
    }

    /**
     * 查询用户指定状态的订单
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param orderStatus 订单状态
     * @return 订单列表
     */
    public Uni<List<Order>> findByUserIdAndStatus(Long tenantId, Long userId, Integer orderStatus) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Order WHERE tenantId = ?1 AND userId = ?2 AND orderStatus = ?3 AND deleted = 0 ORDER BY createTime DESC",
                Order.class
            );
            query.setParameter(1, tenantId);
            query.setParameter(2, userId);
            query.setParameter(3, orderStatus);
            return query.getResultList();
        });
    }

    /**
     * 查询超时未支付的订单
     *
     * @param tenantId 租户ID
     * @param timeoutAt 超时时间
     * @return 订单列表
     */
    public Uni<List<Order>> findTimeoutUnpaidOrders(Long tenantId, LocalDateTime timeoutAt) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Order WHERE tenantId = ?1 AND orderStatus = ?2 AND paymentStatus = ?3 AND timeoutAt <= ?4 AND deleted = 0",
                Order.class
            );
            query.setParameter(1, tenantId);
            query.setParameter(2, Order.Status.PENDING_PAYMENT);
            query.setParameter(3, Order.PaymentStatus.UNPAID);
            query.setParameter(4, timeoutAt);
            return query.getResultList();
        });
    }

    /**
     * 查询需要自动确认收货的订单
     *
     * @param tenantId 租户ID
     * @param autoConfirmTime 自动确认时间
     * @return 订单列表
     */
    public Uni<List<Order>> findAutoConfirmOrders(Long tenantId, LocalDateTime autoConfirmTime) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Order WHERE tenantId = ?1 AND orderStatus = ?2 AND shippingStatus = ?3 AND shippedAt <= ?4 AND deleted = 0",
                Order.class
            );
            query.setParameter(1, tenantId);
            query.setParameter(2, Order.Status.SHIPPED);
            query.setParameter(3, Order.ShippingStatus.SHIPPED);
            query.setParameter(4, autoConfirmTime);
            return query.getResultList();
        });
    }

    /**
     * 根据外部订单号查询订单
     *
     * @param tenantId 租户ID
     * @param externalOrderNumber 外部订单号
     * @return 订单对象
     */
    public Uni<Order> findByExternalOrderNumber(Long tenantId, String externalOrderNumber) {
        return findSingle("FROM Order WHERE tenantId = ?1 AND externalOrderNumber = ?2 AND deleted = 0",
                         Order.class, tenantId, externalOrderNumber);
    }

    /**
     * 统计用户订单数量
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 订单数量
     */
    public Uni<Long> countByUserId(Long tenantId, Long userId) {
        return count("tenantId = ?1 and userId = ?2 and deleted = 0", tenantId, userId);
    }

    /**
     * 统计指定状态的订单数量
     *
     * @param tenantId 租户ID
     * @param orderStatus 订单状态
     * @return 订单数量
     */
    public Uni<Long> countByOrderStatus(Long tenantId, Integer orderStatus) {
        return count("tenantId = ?1 and orderStatus = ?2 and deleted = 0", tenantId, orderStatus);
    }

    /**
     * 统计用户指定状态的订单数量
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param orderStatus 订单状态
     * @return 订单数量
     */
    public Uni<Long> countByUserIdAndStatus(Long tenantId, Long userId, Integer orderStatus) {
        return count("tenantId = ?1 and userId = ?2 and orderStatus = ?3 and deleted = 0",
                    tenantId, userId, orderStatus);
    }

    /**
     * 统计指定时间范围内的订单数量
     *
     * @param tenantId 租户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 订单数量
     */
    public Uni<Long> countByTimeRange(Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return count("tenantId = ?1 and createTime >= ?2 and createTime <= ?3 and deleted = 0",
                    tenantId, startTime, endTime);
    }

    /**
     * 查询指定时间范围内的订单列表
     *
     * @param tenantId 租户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 订单列表
     */
    public Uni<List<Order>> findByTimeRange(Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Order WHERE tenantId = ?1 AND createTime >= ?2 AND createTime <= ?3 AND deleted = 0 ORDER BY createTime DESC",
                Order.class
            );
            query.setParameter(1, tenantId);
            query.setParameter(2, startTime);
            query.setParameter(3, endTime);
            return query.getResultList();
        });
    }

    /**
     * 查询今日订单列表
     *
     * @param tenantId 租户ID
     * @return 订单列表
     */
    public Uni<List<Order>> findTodayOrders(Long tenantId) {
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1);
        return findByTimeRange(tenantId, startOfDay, endOfDay);
    }

    /**
     * 统计今日订单数量
     *
     * @param tenantId 租户ID
     * @return 订单数量
     */
    public Uni<Long> countTodayOrders(Long tenantId) {
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1);
        return countByTimeRange(tenantId, startOfDay, endOfDay);
    }

    /**
     * 根据关键词搜索订单
     *
     * @param tenantId 租户ID
     * @param keyword 关键词（订单号、用户名、收货人等）
     * @return 订单列表
     */
    public Uni<List<Order>> searchOrders(Long tenantId, String keyword) {
        String searchPattern = "%" + keyword + "%";
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Order WHERE tenantId = ?1 AND (orderNumber LIKE ?2 OR userName LIKE ?2 OR receiverName LIKE ?2 OR receiverPhone LIKE ?2) AND deleted = 0 ORDER BY createTime DESC",
                Order.class
            );
            query.setParameter(1, tenantId);
            query.setParameter(2, searchPattern);
            return query.getResultList();
        });
    }

    /**
     * 根据多个条件查询订单
     *
     * @param tenantId 租户ID
     * @param userId 用户ID（可选）
     * @param orderStatus 订单状态（可选）
     * @param paymentStatus 支付状态（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 订单列表
     */
    public Uni<List<Order>> findByMultipleConditions(Long tenantId, Long userId, Integer orderStatus,
                                                     Integer paymentStatus, LocalDateTime startTime, LocalDateTime endTime) {
        StringBuilder query = new StringBuilder("tenantId = ?1 and deleted = 0");
        Object[] params = new Object[7]; // 最多7个参数
        params[0] = tenantId;
        int paramIndex = 1;

        if (userId != null) {
            query.append(" and userId = ?").append(++paramIndex);
            params[paramIndex - 1] = userId;
        }

        if (orderStatus != null) {
            query.append(" and orderStatus = ?").append(++paramIndex);
            params[paramIndex - 1] = orderStatus;
        }

        if (paymentStatus != null) {
            query.append(" and paymentStatus = ?").append(++paramIndex);
            params[paramIndex - 1] = paymentStatus;
        }

        if (startTime != null) {
            query.append(" and createTime >= ?").append(++paramIndex);
            params[paramIndex - 1] = startTime;
        }

        if (endTime != null) {
            query.append(" and createTime <= ?").append(++paramIndex);
            params[paramIndex - 1] = endTime;
        }

        query.append(" order by createTime desc");

        // 创建实际参数数组
        Object[] actualParams = new Object[paramIndex];
        System.arraycopy(params, 0, actualParams, 0, paramIndex);

        return executeQuery(session -> {
            var hqlQuery = session.createQuery("FROM Order WHERE " + query.toString(), Order.class);
            for (int i = 0; i < actualParams.length; i++) {
                hqlQuery.setParameter(i + 1, actualParams[i]);
            }
            return hqlQuery.getResultList();
        });
    }
}
