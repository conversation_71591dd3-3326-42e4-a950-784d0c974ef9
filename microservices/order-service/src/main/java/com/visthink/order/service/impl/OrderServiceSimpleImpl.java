package com.visthink.order.service.impl;

import com.visthink.order.dto.OrderCreateRequest;
import com.visthink.order.dto.OrderQueryRequest;
import com.visthink.order.dto.OrderUpdateRequest;
import com.visthink.order.entity.Order;
import com.visthink.order.repository.OrderRepository;
import com.visthink.order.service.OrderService;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.transaction.Transactional;
import org.jboss.logging.Logger;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 订单服务简化实现
 *
 * 提供基本的订单管理功能，用于快速启动和测试
 *
 * <AUTHOR>
 */
@ApplicationScoped
@Named("orderServiceSimple")
public class OrderServiceSimpleImpl implements OrderService {

    private static final Logger LOG = Logger.getLogger(OrderServiceSimpleImpl.class);
    private static final AtomicInteger ORDER_SEQUENCE = new AtomicInteger(1);

    @Inject
    OrderRepository orderRepository;

    @Override
    @Transactional
    public Uni<Order> createOrder(Long tenantId, OrderCreateRequest request) {
        LOG.infof("创建订单，租户ID: %d", tenantId);

        Order order = new Order();
        order.setTenantId(tenantId);
        order.setOrderNo(generateOrderNumber());
        order.setCustomerId(request.getCustomerId());
        order.setCustomerName(request.getCustomerName());
        order.setOrderType(request.getOrderType() != null ? request.getOrderType() : 1);
        order.setOrderSource(request.getOrderSource() != null ? request.getOrderSource() : 1);
        order.setReceiverName(request.getReceiverName());
        order.setReceiverPhone(request.getReceiverPhone());
        order.setReceiverAddress(request.getReceiverAddress());
        order.setGoodsAmount(request.calculateGoodsAmount());
        order.setShippingAmount(request.getShippingAmount() != null ? request.getShippingAmount() : BigDecimal.ZERO);
        order.setDiscountAmount(request.getDiscountAmount() != null ? request.getDiscountAmount() : BigDecimal.ZERO);
        order.setTaxAmount(request.getTaxAmount() != null ? request.getTaxAmount() : BigDecimal.ZERO);
        order.calculateTotalAmount();
        order.setCreateTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());

        return orderRepository.persist(order);
    }

    @Override
    public Uni<Order> getOrderById(Long tenantId, Long orderId) {
        return orderRepository.findById(orderId)
                .onItem().transform(order -> {
                    if (order != null && order.getTenantId().equals(tenantId)) {
                        return order;
                    }
                    return null;
                });
    }

    @Override
    @Transactional
    public Uni<Order> updateOrder(Long tenantId, Long orderId, OrderUpdateRequest request) {
        return getOrderById(tenantId, orderId)
                .onItem().transformToUni(order -> {
                    if (order == null) {
                        return Uni.createFrom().failure(new RuntimeException("订单不存在"));
                    }
                    if (request.getReceiverName() != null) {
                        order.setReceiverName(request.getReceiverName());
                    }
                    if (request.getReceiverPhone() != null) {
                        order.setReceiverPhone(request.getReceiverPhone());
                    }
                    if (request.getReceiverAddress() != null) {
                        order.setReceiverAddress(request.getReceiverAddress());
                    }
                    order.setUpdateTime(LocalDateTime.now());
                    return orderRepository.persist(order);
                });
    }

    @Override
    @Transactional
    public Uni<Boolean> deleteOrder(Long tenantId, Long orderId) {
        return getOrderById(tenantId, orderId)
                .onItem().transformToUni(order -> {
                    if (order != null) {
                        return orderRepository.deleteById(orderId)
                                .onItem().transform(deleted -> deleted);
                    }
                    return Uni.createFrom().item(false);
                });
    }

    @Override
    public Uni<PageResult<Order>> getOrderList(Long tenantId, PageRequest pageRequest, String status, String customerName) {
        // 简化实现：基础分页查询
        int offset = (pageRequest.getPage() - 1) * pageRequest.getSize();

        // 查询总数
        Uni<Long> totalUni = orderRepository.count("tenantId", tenantId);

        // 查询数据
        Uni<List<Order>> dataUni = orderRepository.list("tenantId", tenantId);

        // 组合结果
        return Uni.combine().all().unis(totalUni, dataUni)
                .asTuple()
                .map(tuple -> {
                    Long total = tuple.getItem1();
                    List<Order> data = tuple.getItem2();

                    PageResult<Order> result = new PageResult<>();
                    result.setRecords(data);
                    result.setTotal(total);
                    result.setPageNum(pageRequest.getPage());
                    result.setPageSize(pageRequest.getSize());

                    return result;
                });
    }

    @Override
    @Transactional
    public Uni<Boolean> cancelOrder(Long tenantId, Long orderId, String reason) {
        return getOrderById(tenantId, orderId)
                .onItem().transformToUni(order -> {
                    if (order == null) {
                        return Uni.createFrom().failure(new RuntimeException("订单不存在"));
                    }
                    order.setOrderStatus("CANCELLED");
                    order.setCancelReason(reason);
                    order.setCancelTime(LocalDateTime.now());
                    order.setUpdateTime(LocalDateTime.now());
                    return orderRepository.persist(order)
                            .onItem().transform(updatedOrder -> true);
                });
    }

    @Override
    @Transactional
    public Uni<Boolean> confirmOrder(Long tenantId, Long orderId) {
        return getOrderById(tenantId, orderId)
                .onItem().transformToUni(order -> {
                    if (order == null) {
                        return Uni.createFrom().failure(new RuntimeException("订单不存在"));
                    }
                    order.setOrderStatus("CONFIRMED");
                    order.setConfirmTime(LocalDateTime.now());
                    order.setUpdateTime(LocalDateTime.now());
                    return orderRepository.persist(order)
                            .onItem().transform(updatedOrder -> true);
                });
    }

    @Override
    @Transactional
    public Uni<Boolean> shipOrder(Long tenantId, Long orderId) {
        return getOrderById(tenantId, orderId)
                .onItem().transformToUni(order -> {
                    if (order == null) {
                        return Uni.createFrom().failure(new RuntimeException("订单不存在"));
                    }
                    order.setOrderStatus("SHIPPED");
                    order.setShippingStatus("SHIPPED");
                    order.setShipTime(LocalDateTime.now());
                    order.setUpdateTime(LocalDateTime.now());
                    return orderRepository.persist(order)
                            .onItem().transform(updatedOrder -> true);
                });
    }

    @Override
    @Transactional
    public Uni<Boolean> completeOrder(Long tenantId, Long orderId) {
        return getOrderById(tenantId, orderId)
                .onItem().transformToUni(order -> {
                    if (order == null) {
                        return Uni.createFrom().failure(new RuntimeException("订单不存在"));
                    }
                    order.setOrderStatus("COMPLETED");
                    order.setCompleteTime(LocalDateTime.now());
                    order.setUpdateTime(LocalDateTime.now());
                    return orderRepository.persist(order)
                            .onItem().transform(updatedOrder -> true);
                });
    }



    private String generateOrderNumber() {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        int sequence = ORDER_SEQUENCE.getAndIncrement() % 1000;
        return String.format("ORD%s%03d", dateStr, sequence);
    }
}
