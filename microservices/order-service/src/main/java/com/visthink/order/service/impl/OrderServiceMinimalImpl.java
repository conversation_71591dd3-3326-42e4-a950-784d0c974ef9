package com.visthink.order.service.impl;

import com.visthink.order.entity.Order;
import com.visthink.order.repository.OrderRepository;
import com.visthink.order.service.OrderServiceMinimal;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.PageRequest;
import io.smallrye.mutiny.Uni;
import org.jboss.logging.Logger;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import java.util.List;

/**
 * 订单服务实现类 - 最小化版本
 *
 * 提供订单管理的基础功能实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class OrderServiceMinimalImpl implements OrderServiceMinimal {

    private static final Logger log = Logger.getLogger(OrderServiceMinimalImpl.class);

    @Inject
    OrderRepository orderRepository;

    @Override
    public Uni<Order> findById(Long tenantId, Long orderId) {
        log.infof("查询订单: tenantId=%d, orderId=%d", tenantId, orderId);
        return orderRepository.findById(orderId)
                .map(order -> {
                    // 验证租户ID
                    if (order != null && !tenantId.equals(order.getTenantId())) {
                        return null; // 不属于当前租户
                    }
                    return order;
                });
    }

    @Override
    public Uni<List<Order>> findAll(Long tenantId) {
        log.infof("查询所有订单: tenantId=%d", tenantId);
        return orderRepository.list("tenantId", tenantId);
    }

    @Override
    public Uni<PageResult<Order>> findPage(Long tenantId, PageRequest pageRequest) {
        log.infof("分页查询订单: tenantId=%d, page=%d, size=%d",
                 tenantId, pageRequest.getPage(), pageRequest.getSize());

        // 计算偏移量
        int offset = (pageRequest.getPage() - 1) * pageRequest.getSize();

        // 查询总数
        Uni<Long> totalUni = orderRepository.count("tenantId", tenantId);

        // 查询数据
        Uni<List<Order>> dataUni = orderRepository.list("tenantId", tenantId);

        // 组合结果
        return Uni.combine().all().unis(totalUni, dataUni)
                .asTuple()
                .map(tuple -> {
                    Long total = tuple.getItem1();
                    List<Order> data = tuple.getItem2();

                    PageResult<Order> result = new PageResult<>();
                    result.setRecords(data);
                    result.setTotal(total);
                    result.setPageNum(pageRequest.getPage());
                    result.setPageSize(pageRequest.getSize());

                    return result;
                });
    }

    @Override
    @Transactional
    public Uni<Order> save(Long tenantId, Order order) {
        log.infof("保存订单: tenantId=%d, order=%s", tenantId, order);

        // 设置租户ID
        order.setTenantId(tenantId);

        return orderRepository.persist(order);
    }

    @Override
    @Transactional
    public Uni<Boolean> deleteById(Long tenantId, Long orderId) {
        log.infof("删除订单: tenantId=%d, orderId=%d", tenantId, orderId);

        return findById(tenantId, orderId)
                .flatMap(order -> {
                    if (order == null) {
                        return Uni.createFrom().item(false);
                    }
                    return orderRepository.delete(order)
                            .map(deleted -> true);
                });
    }

    @Override
    public Uni<Boolean> exists(Long tenantId, Long orderId) {
        log.infof("检查订单是否存在: tenantId=%d, orderId=%d", tenantId, orderId);

        return findById(tenantId, orderId)
                .map(order -> order != null);
    }
}
