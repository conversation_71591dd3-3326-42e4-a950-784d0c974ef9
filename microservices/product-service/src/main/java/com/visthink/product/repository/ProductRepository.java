package com.visthink.product.repository;

import com.visthink.product.entity.Product;
import com.visthink.common.repository.ReactiveBaseRepository;
import io.quarkus.panache.common.Page;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

/**
 * 商品数据访问层
 * 继承ReactiveBaseRepository获得通用CRUD功能，支持多租户数据隔离
 *
 * 重构说明：
 * 1. 从BaseRepository接口迁移到ReactiveBaseRepository类
 * 2. 使用纯Hibernate Reactive，不依赖Panache
 * 3. 所有方法返回Uni<T>，支持响应式编程
 * 4. 使用executeQuery/executeTransaction模式确保线程安全
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class ProductRepository extends ReactiveBaseRepository {

    /**
     * 根据租户ID分页查询商品
     *
     * @param tenantId 租户ID
     * @param page 分页参数
     * @return 商品列表
     */
    public Uni<List<Product>> findByTenantId(Long tenantId, Page page) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Product WHERE tenantId = ?1 AND deleted = 0 ORDER BY id DESC",
                Product.class
            );
            query.setParameter(1, tenantId);
            query.setFirstResult(page.index * page.size);
            query.setMaxResults(page.size);
            return query.getResultList();
        });
    }

    /**
     * 根据租户ID和商品编码查询
     *
     * @param tenantId 租户ID
     * @param productCode 商品编码
     * @return 商品对象
     */
    public Uni<Product> findByTenantAndCode(Long tenantId, String productCode) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Product WHERE tenantId = ?1 AND productCode = ?2 AND deleted = 0",
                Product.class
            );
            query.setParameter(1, tenantId);
            query.setParameter(2, productCode);
            return query.getSingleResultOrNull();
        });
    }

    /**
     * 检查商品编码是否存在
     *
     * @param tenantId 租户ID
     * @param productCode 商品编码
     * @return 是否存在
     */
    public Uni<Boolean> existsByTenantAndCode(Long tenantId, String productCode) {
        return count("SELECT COUNT(*) FROM Product WHERE tenantId = ?1 AND productCode = ?2 AND deleted = 0",
                    tenantId, productCode)
                .map(count -> count > 0);
    }

    /**
     * 根据状态查询商品
     *
     * @param tenantId 租户ID
     * @param status 商品状态
     * @param page 分页参数
     * @return 商品列表
     */
    public Uni<List<Product>> findByTenantAndStatus(Long tenantId, Integer status, Page page) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Product WHERE tenantId = ?1 AND status = ?2 AND deleted = 0 ORDER BY id DESC",
                Product.class
            );
            query.setParameter(1, tenantId);
            query.setParameter(2, status);
            query.setFirstResult(page.index * page.size);
            query.setMaxResults(page.size);
            return query.getResultList();
        });
    }

    /**
     * 根据分类查询商品
     *
     * @param tenantId 租户ID
     * @param categoryId 分类ID
     * @param page 分页参数
     * @return 商品列表
     */
    public Uni<List<Product>> findByTenantAndCategory(Long tenantId, Long categoryId, Page page) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Product WHERE tenantId = ?1 AND categoryId = ?2 AND deleted = 0 ORDER BY id DESC",
                Product.class
            );
            query.setParameter(1, tenantId);
            query.setParameter(2, categoryId);
            query.setFirstResult(page.index * page.size);
            query.setMaxResults(page.size);
            return query.getResultList();
        });
    }

    /**
     * 搜索商品
     *
     * @param tenantId 租户ID
     * @param keyword 搜索关键词
     * @param page 分页参数
     * @return 商品列表
     */
    public Uni<List<Product>> searchProducts(Long tenantId, String keyword, Page page) {
        return executeQuery(session -> {
            String hql = "FROM Product WHERE tenantId = ?1 AND (productName LIKE ?2 OR productCode LIKE ?2 OR keywords LIKE ?2) AND deleted = 0 ORDER BY id DESC";
            String searchKeyword = "%" + keyword + "%";
            var query = session.createQuery(hql, Product.class);
            query.setParameter(1, tenantId);
            query.setParameter(2, searchKeyword);
            query.setFirstResult(page.index * page.size);
            query.setMaxResults(page.size);
            return query.getResultList();
        });
    }

    /**
     * 统计商品总数
     *
     * @param tenantId 租户ID
     * @return 商品总数
     */
    public Uni<Long> countByTenant(Long tenantId) {
        return count("SELECT COUNT(*) FROM Product WHERE tenantId = ?1 AND deleted = 0", tenantId);
    }

    /**
     * 统计指定状态的商品数量
     *
     * @param tenantId 租户ID
     * @param status 商品状态
     * @return 商品数量
     */
    public Uni<Long> countByTenantAndStatus(Long tenantId, Integer status) {
        return count("SELECT COUNT(*) FROM Product WHERE tenantId = ?1 AND status = ?2 AND deleted = 0",
                    tenantId, status);
    }

    /**
     * 根据ID列表查询商品
     *
     * @param tenantId 租户ID
     * @param productIds 商品ID列表
     * @return 商品列表
     */
    public Uni<List<Product>> findByTenantAndIds(Long tenantId, List<Long> productIds) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Product WHERE tenantId = ?1 AND id IN (?2) AND deleted = 0",
                Product.class
            );
            query.setParameter(1, tenantId);
            query.setParameter(2, productIds);
            return query.getResultList();
        });
    }

    /**
     * 批量更新商品状态
     *
     * @param tenantId 租户ID
     * @param productIds 商品ID列表
     * @param status 新状态
     * @return 更新数量
     */
    public Uni<Integer> updateStatusByIds(Long tenantId, List<Long> productIds, Integer status) {
        return executeUpdate(session -> {
            var query = session.createMutationQuery(
                "UPDATE Product SET status = ?1, updateTime = current_timestamp WHERE tenantId = ?2 AND id IN (?3) AND deleted = 0"
            );
            query.setParameter(1, status);
            query.setParameter(2, tenantId);
            query.setParameter(3, productIds);
            return query.executeUpdate();
        });
    }

    /**
     * 获取热销商品
     *
     * @param tenantId 租户ID
     * @param page 分页参数
     * @return 热销商品列表
     */
    public Uni<List<Product>> findHotProducts(Long tenantId, Page page) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Product WHERE tenantId = ?1 AND isHot = true AND status = 1 AND deleted = 0 ORDER BY salesCount DESC, id DESC",
                Product.class
            );
            query.setParameter(1, tenantId);
            query.setFirstResult(page.index * page.size);
            query.setMaxResults(page.size);
            return query.getResultList();
        });
    }

    /**
     * 获取推荐商品
     *
     * @param tenantId 租户ID
     * @param page 分页参数
     * @return 推荐商品列表
     */
    public Uni<List<Product>> findRecommendProducts(Long tenantId, Page page) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Product WHERE tenantId = ?1 AND isRecommend = true AND status = 1 AND deleted = 0 ORDER BY sortOrder DESC, id DESC",
                Product.class
            );
            query.setParameter(1, tenantId);
            query.setFirstResult(page.index * page.size);
            query.setMaxResults(page.size);
            return query.getResultList();
        });
    }

    /**
     * 获取新品
     *
     * @param tenantId 租户ID
     * @param page 分页参数
     * @return 新品列表
     */
    public Uni<List<Product>> findNewProducts(Long tenantId, Page page) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Product WHERE tenantId = ?1 AND isNew = true AND status = 1 AND deleted = 0 ORDER BY createdAt DESC",
                Product.class
            );
            query.setParameter(1, tenantId);
            query.setFirstResult(page.index * page.size);
            query.setMaxResults(page.size);
            return query.getResultList();
        });
    }

    /**
     * 根据价格区间查询商品
     *
     * @param tenantId 租户ID
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @param page 分页参数
     * @return 商品列表
     */
    public Uni<List<Product>> findByPriceRange(Long tenantId, Double minPrice, Double maxPrice, Page page) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM Product WHERE tenantId = ?1 AND salePrice >= ?2 AND salePrice <= ?3 AND status = 1 AND deleted = 0 ORDER BY salePrice ASC",
                Product.class
            );
            query.setParameter(1, tenantId);
            query.setParameter(2, minPrice);
            query.setParameter(3, maxPrice);
            query.setFirstResult(page.index * page.size);
            query.setMaxResults(page.size);
            return query.getResultList();
        });
    }

    /**
     * 更新商品浏览量
     *
     * @param productId 商品ID
     * @return 更新数量
     */
    public Uni<Integer> incrementViewCount(Long productId) {
        return executeUpdate(session -> {
            var query = session.createMutationQuery(
                "UPDATE Product SET viewCount = viewCount + 1, updateTime = current_timestamp WHERE id = ?1 AND deleted = 0"
            );
            query.setParameter(1, productId);
            return query.executeUpdate();
        });
    }

    /**
     * 更新商品销量
     *
     * @param productId 商品ID
     * @param quantity 销量增量
     * @return 更新数量
     */
    public Uni<Integer> incrementSalesCount(Long productId, Integer quantity) {
        return executeUpdate(session -> {
            var query = session.createMutationQuery(
                "UPDATE Product SET salesCount = salesCount + ?1, updateTime = current_timestamp WHERE id = ?2 AND deleted = 0"
            );
            query.setParameter(1, quantity);
            query.setParameter(2, productId);
            return query.executeUpdate();
        });
    }

    /**
     * 根据ID查找产品
     *
     * @param id 产品ID
     * @return 产品对象
     */
    public Uni<Product> findById(Long id) {
        return super.findById(id, Product.class);
    }
}
