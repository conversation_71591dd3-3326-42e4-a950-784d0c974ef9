package com.visthink.product.repository;

import com.visthink.product.entity.ProductSku;
import com.visthink.common.repository.ReactiveBaseRepository;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

/**
 * 商品SKU数据访问层
 * 继承ReactiveBaseRepository获得通用CRUD功能，支持多租户数据隔离
 *
 * 重构说明：
 * 1. 从BaseRepository接口迁移到ReactiveBaseRepository类
 * 2. 使用纯Hibernate Reactive，不依赖Panache
 * 3. 所有方法返回Uni<T>，支持响应式编程
 * 4. 使用executeQuery/executeTransaction模式确保线程安全
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class ProductSkuRepository extends ReactiveBaseRepository {

    /**
     * 根据商品ID查询SKU列表
     *
     * @param productId 商品ID
     * @return SKU列表
     */
    public Uni<List<ProductSku>> findByProductId(Long productId) {
        return findList("FROM ProductSku WHERE productId = ?1 AND deleted = 0 ORDER BY sortOrder, id",
                       ProductSku.class, productId);
    }

    /**
     * 根据租户ID和SKU编码查询
     *
     * @param tenantId 租户ID
     * @param skuCode SKU编码
     * @return SKU对象
     */
    public Uni<ProductSku> findByTenantAndSkuCode(Long tenantId, String skuCode) {
        return executeQuery(session -> {
            var query = session.createQuery(
                "FROM ProductSku WHERE tenantId = ?1 AND skuCode = ?2 AND deleted = 0",
                ProductSku.class
            );
            query.setParameter(1, tenantId);
            query.setParameter(2, skuCode);
            return query.getSingleResultOrNull();
        });
    }

    /**
     * 检查SKU编码是否存在
     *
     * @param tenantId 租户ID
     * @param skuCode SKU编码
     * @return 是否存在
     */
    public Uni<Boolean> existsByTenantAndSkuCode(Long tenantId, String skuCode) {
        return count("SELECT COUNT(*) FROM ProductSku WHERE tenantId = ?1 AND skuCode = ?2 AND deleted = 0",
                    tenantId, skuCode)
                .map(count -> count > 0);
    }

    /**
     * 根据商品ID列表查询所有SKU
     *
     * @param productIds 商品ID列表
     * @return SKU列表
     */
    public Uni<List<ProductSku>> findByProductIds(List<Long> productIds) {
        return find("productId in ?1 and deleted = 0 order by productId, sortOrder, id", productIds).list();
    }

    /**
     * 根据租户ID和商品ID查询SKU
     *
     * @param tenantId 租户ID
     * @param productId 商品ID
     * @return SKU列表
     */
    public Uni<List<ProductSku>> findByTenantAndProductId(Long tenantId, Long productId) {
        return find("tenantId = ?1 and productId = ?2 and deleted = 0 order by sortOrder, id", tenantId, productId).list();
    }

    /**
     * 根据状态查询SKU
     *
     * @param tenantId 租户ID
     * @param status SKU状态
     * @return SKU列表
     */
    public Uni<List<ProductSku>> findByTenantAndStatus(Long tenantId, Integer status) {
        return find("tenantId = ?1 and status = ?2 and deleted = 0 order by id desc", tenantId, status).list();
    }

    /**
     * 统计商品的SKU数量
     *
     * @param productId 商品ID
     * @return SKU数量
     */
    public Uni<Long> countByProductId(Long productId) {
        return count("productId = ?1 and deleted = 0", productId);
    }

    /**
     * 统计租户的SKU总数
     *
     * @param tenantId 租户ID
     * @return SKU总数
     */
    public Uni<Long> countByTenant(Long tenantId) {
        return count("tenantId = ?1 and deleted = 0", tenantId);
    }

    /**
     * 批量软删除商品的SKU
     *
     * @param tenantId 租户ID
     * @param productId 商品ID
     * @return 删除数量
     */
    public Uni<Integer> softDeleteByProductId(Long tenantId, Long productId) {
        return update("deleted = 1, updateTime = current_timestamp where tenantId = ?1 and productId = ?2 and deleted = 0",
                tenantId, productId);
    }

    /**
     * 批量更新SKU状态
     *
     * @param tenantId 租户ID
     * @param productId 商品ID
     * @param status 新状态
     * @return 更新数量
     */
    public Uni<Integer> updateStatusByProductId(Long tenantId, Long productId, Integer status) {
        return update("status = ?1, updateTime = current_timestamp where tenantId = ?2 and productId = ?3 and deleted = 0",
                status, tenantId, productId);
    }

    /**
     * 批量更新SKU状态（根据ID列表）
     *
     * @param tenantId 租户ID
     * @param skuIds SKU ID列表
     * @param status 新状态
     * @return 更新数量
     */
    public Uni<Integer> updateStatusByIds(Long tenantId, List<Long> skuIds, Integer status) {
        return update("status = ?1, updateTime = current_timestamp where tenantId = ?2 and id in ?3 and deleted = 0",
                status, tenantId, skuIds);
    }

    /**
     * 更新SKU库存
     *
     * @param tenantId 租户ID
     * @param skuId SKU ID
     * @param stockQuantity 库存数量
     * @return 更新数量
     */
    public Uni<Integer> updateStock(Long tenantId, Long skuId, Integer stockQuantity) {
        return update("stockQuantity = ?1, updateTime = current_timestamp where tenantId = ?2 and id = ?3 and deleted = 0",
                stockQuantity, tenantId, skuId);
    }

    /**
     * 增加SKU库存
     *
     * @param tenantId 租户ID
     * @param skuId SKU ID
     * @param quantity 增加数量
     * @return 更新数量
     */
    public Uni<Integer> increaseStock(Long tenantId, Long skuId, Integer quantity) {
        return update("stockQuantity = stockQuantity + ?1, updateTime = current_timestamp where tenantId = ?2 and id = ?3 and deleted = 0",
                quantity, tenantId, skuId);
    }

    /**
     * 减少SKU库存
     *
     * @param tenantId 租户ID
     * @param skuId SKU ID
     * @param quantity 减少数量
     * @return 更新数量
     */
    public Uni<Integer> decreaseStock(Long tenantId, Long skuId, Integer quantity) {
        return update("stockQuantity = stockQuantity - ?1, updateTime = current_timestamp where tenantId = ?2 and id = ?3 and stockQuantity >= ?1 and deleted = 0",
                quantity, tenantId, skuId);
    }

    /**
     * 更新SKU销量
     *
     * @param tenantId 租户ID
     * @param skuId SKU ID
     * @param quantity 销量增量
     * @return 更新数量
     */
    public Uni<Integer> incrementSalesCount(Long tenantId, Long skuId, Integer quantity) {
        return update("salesCount = salesCount + ?1, updateTime = current_timestamp where tenantId = ?2 and id = ?3 and deleted = 0",
                quantity, tenantId, skuId);
    }

    /**
     * 查询库存不足的SKU
     *
     * @param tenantId 租户ID
     * @return 库存不足的SKU列表
     */
    public Uni<List<ProductSku>> findLowStockSkus(Long tenantId) {
        return find("tenantId = ?1 and stockQuantity <= warningStock and status = 1 and deleted = 0", tenantId).list();
    }

    /**
     * 根据租户ID和条形码查询SKU
     *
     * @param tenantId 租户ID
     * @param barcode 条形码
     * @return SKU对象
     */
    public Uni<ProductSku> findByTenantAndBarcode(Long tenantId, String barcode) {
        return find("tenantId = ?1 and barcode = ?2 and deleted = 0", tenantId, barcode).firstResult();
    }
}
