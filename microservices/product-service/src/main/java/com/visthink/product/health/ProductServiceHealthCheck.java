package com.visthink.product.health;

import com.visthink.product.repository.ProductRepository;
import io.smallrye.health.checks.UrlHealthCheck;
import org.eclipse.microprofile.health.HealthCheck;
import org.eclipse.microprofile.health.HealthCheckResponse;
import org.eclipse.microprofile.health.Liveness;
import org.eclipse.microprofile.health.Readiness;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

/**
 * 商品服务健康检查
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class ProductServiceHealthCheck {

    @Inject
    ProductRepository productRepository;

    @Liveness
    public HealthCheck livenessCheck() {
        return () -> HealthCheckResponse.named("product-service-liveness")
                .status(true)
                .withData("service", "product-service")
                .withData("version", "1.0.0")
                .withData("status", "UP")
                .build();
    }

    @Readiness
    public HealthCheck readinessCheck() {
        return () -> {
            try {
                // 检查数据库连接
                productRepository.count("SELECT COUNT(*) FROM Product").await().indefinitely();

                return HealthCheckResponse.named("product-service-readiness")
                        .status(true)
                        .withData("database", "connected")
                        .withData("service", "product-service")
                        .withData("status", "READY")
                        .build();
            } catch (Exception e) {
                return HealthCheckResponse.named("product-service-readiness")
                        .status(false)
                        .withData("database", "disconnected")
                        .withData("service", "product-service")
                        .withData("status", "NOT_READY")
                        .withData("error", e.getMessage())
                        .build();
            }
        };
    }
}
