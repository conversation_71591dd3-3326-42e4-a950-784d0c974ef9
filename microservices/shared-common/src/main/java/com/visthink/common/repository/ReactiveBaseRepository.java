package com.visthink.member.repository;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import org.hibernate.reactive.mutiny.Mutiny;
import org.jboss.logging.Logger;

import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Function;

/**
 * 响应式基础Repository
 * 
 * 提供安全的Hibernate Reactive会话管理
 * 解决JdbcValuesSourceProcessingState错误
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public abstract class ReactiveBaseRepository {

    private static final Logger LOG = Logger.getLogger(ReactiveBaseRepository.class);

    @Inject
    protected Mutiny.SessionFactory sessionFactory;

    /**
     * 执行查询操作（只读会话）
     * 
     * @param operation 查询操作
     * @param <T> 返回类型
     * @return 查询结果
     */
    protected <T> Uni<T> executeQuery(Function<Mutiny.Session, Uni<T>> operation) {
        return sessionFactory.withSession(session -> {
            try {
                return operation.apply(session);
            } catch (Exception e) {
                LOG.errorf(e, "执行查询操作失败");
                return Uni.createFrom().failure(e);
            }
        }).onFailure().invoke(throwable -> {
            LOG.errorf(throwable, "查询操作执行失败");
        });
    }

    /**
     * 执行事务操作（读写会话）
     * 
     * @param operation 事务操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    protected <T> Uni<T> executeTransaction(Function<Mutiny.Session, Uni<T>> operation) {
        return sessionFactory.withTransaction((session, transaction) -> {
            try {
                // 移除手动flush调用，让withTransaction自动管理事务提交
                // 避免"Session/EntityManager is closed"和"Transaction already complete"错误
                return operation.apply(session);
            } catch (Exception e) {
                LOG.errorf(e, "执行事务操作失败");
                return Uni.createFrom().failure(e);
            }
        }).onFailure().invoke(throwable -> {
            LOG.errorf(throwable, "事务操作执行失败");
        });
    }

    /**
     * 执行单个实体查询
     * 
     * @param hql HQL查询语句
     * @param entityClass 实体类
     * @param parameters 查询参数
     * @param <T> 实体类型
     * @return 查询结果
     */
    protected <T> Uni<T> findSingle(String hql, Class<T> entityClass, Object... parameters) {
        return executeQuery(session -> {
            var query = session.createQuery(hql, entityClass);
            
            // 设置参数
            for (int i = 0; i < parameters.length; i += 2) {
                if (i + 1 < parameters.length) {
                    query.setParameter((String) parameters[i], parameters[i + 1]);
                }
            }
            
            return query.getSingleResultOrNull();
        });
    }

    /**
     * 执行列表查询
     * 
     * @param hql HQL查询语句
     * @param entityClass 实体类
     * @param parameters 查询参数
     * @param <T> 实体类型
     * @return 查询结果列表
     */
    protected <T> Uni<List<T>> findList(String hql, Class<T> entityClass, Object... parameters) {
        return executeQuery(session -> {
            var query = session.createQuery(hql, entityClass);
            
            // 设置参数
            for (int i = 0; i < parameters.length; i += 2) {
                if (i + 1 < parameters.length) {
                    query.setParameter((String) parameters[i], parameters[i + 1]);
                }
            }
            
            return query.getResultList();
        });
    }

    /**
     * 执行计数查询
     * 
     * @param hql HQL查询语句
     * @param parameters 查询参数
     * @return 计数结果
     */
    protected Uni<Long> count(String hql, Object... parameters) {
        return executeQuery(session -> {
            var query = session.createQuery(hql, Long.class);
            
            // 设置参数
            for (int i = 0; i < parameters.length; i += 2) {
                if (i + 1 < parameters.length) {
                    query.setParameter((String) parameters[i], parameters[i + 1]);
                }
            }
            
            return query.getSingleResult();
        });
    }

    /**
     * 保存实体
     * 
     * @param entity 实体对象
     * @param <T> 实体类型
     * @return 保存后的实体
     */
    protected <T> Uni<T> persist(T entity) {
        return executeTransaction(session -> {
            return session.persist(entity)
                .replaceWith(entity);
        });
    }

    /**
     * 更新实体
     * 
     * @param entity 实体对象
     * @param <T> 实体类型
     * @return 更新后的实体
     */
    protected <T> Uni<T> merge(T entity) {
        return executeTransaction(session -> {
            return session.merge(entity);
        });
    }

    /**
     * 删除实体
     * 
     * @param entity 实体对象
     * @return 删除操作结果
     */
    protected <T> Uni<Void> remove(T entity) {
        return executeTransaction(session -> {
            return session.remove(entity);
        });
    }

    /**
     * 获取当前时间
     * 
     * @return 当前时间
     */
    protected LocalDateTime now() {
        return LocalDateTime.now();
    }

    /**
     * 记录调试信息
     * 
     * @param message 消息
     * @param params 参数
     */
    protected void logDebug(String message, Object... params) {
        if (LOG.isDebugEnabled()) {
            LOG.debugf(message, params);
        }
    }

    /**
     * 记录错误信息
     * 
     * @param throwable 异常
     * @param message 消息
     * @param params 参数
     */
    protected void logError(Throwable throwable, String message, Object... params) {
        LOG.errorf(throwable, message, params);
    }
}
